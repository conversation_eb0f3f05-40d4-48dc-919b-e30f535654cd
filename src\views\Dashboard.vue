<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon employee">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ employeeCount }}</div>
              <div class="stat-label">员工总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon department">
              <el-icon><OfficeBuilding /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ departmentCount }}</div>
              <div class="stat-label">部门总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ activeEmployeeCount }}</div>
              <div class="stat-label">在职员工</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon inactive">
              <el-icon><CircleClose /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ inactiveEmployeeCount }}</div>
              <div class="stat-label">离职员工</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>快捷操作</span>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="goToAddEmployee">
              <el-icon><Plus /></el-icon>
              添加员工
            </el-button>
            <el-button type="success" @click="goToAddDepartment">
              <el-icon><Plus /></el-icon>
              添加部门
            </el-button>
            <el-button type="info" @click="goToEmployeeList">
              <el-icon><List /></el-icon>
              员工列表
            </el-button>
            <el-button type="warning" @click="goToDepartmentList">
              <el-icon><List /></el-icon>
              部门列表
            </el-button>
            <el-button type="danger" @click="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-button>

          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <span>系统信息</span>
          </template>
          <div class="system-info">
            <p><strong>系统名称：</strong>员工管理系统</p>
            <p><strong>版本：</strong>v1.0.0</p>
            <p><strong>最后更新：</strong>{{ new Date().toLocaleDateString() }}</p>
            <p><strong>管理员：</strong>系统管理员</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { get } from '../api/api.js'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 统计数据
const employeeCount = ref(0)
const departmentCount = ref(0)
const activeEmployeeCount = ref(0)
const inactiveEmployeeCount = ref(0)

// 获取统计数据
const getStatistics = async () => {
  try {
    // 获取员工统计
    const employeeResponse = await get('/employee/list', { currentPage: 1, pageSize: 1000 })
    if (employeeResponse.code === 200) {
      const employees = employeeResponse.data
      employeeCount.value = employees.length
      activeEmployeeCount.value = employees.filter(emp => emp.status === 1).length
      inactiveEmployeeCount.value = employees.filter(emp => emp.status === 0).length
    }

    // 获取部门统计
    const departmentResponse = await get('/department/all')
    if (departmentResponse.code === 200) {
      departmentCount.value = departmentResponse.data.length
    }
  } catch (error) {
    ElMessage.error('获取统计数据失败')
  }
}

// 快捷操作导航
const goToAddEmployee = () => {
  router.push('/employee/list')
}

const goToAddDepartment = () => {
  router.push('/department/list')
}

const goToEmployeeList = () => {
  router.push('/employee/list')
}

const goToDepartmentList = () => {
  router.push('/department/list')
}

const logout = () => {
  localStorage.removeItem('admin')
  ElMessage.success('已退出登录')
  router.replace('/login')
}

// 页面加载时获取数据
onMounted(() => {
  getStatistics()
})
</script>

<style scoped>
/**
 * Modern Dashboard Styling
 * Glassmorphism design with enhanced visual hierarchy
 */

.dashboard {
  padding: 24px;
  background: transparent;
  min-height: 100%;
}

/* 简洁的统计卡片 */
:deep(.stat-card) {
  height: 120px;
  background: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

:deep(.stat-card:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.stat-card .el-card__body) {
  padding: 20px;
  height: 100%;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-icon.employee {
  background: #5b9bd5;
}

.stat-icon.department {
  background: #e91e63;
}

.stat-icon.active {
  background: #00bcd4;
}

.stat-icon.inactive {
  background: #4caf50;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
  font-weight: 400;
}

/* 简洁的卡片样式 */
:deep(.el-card) {
  background: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.el-card:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e6e6e6;
  padding: 16px 20px;
  font-weight: 500;
  color: #333;
  font-size: 16px;
}

:deep(.el-card__body) {
  padding: 20px;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

:deep(.quick-actions .el-button) {
  margin: 0;
  height: 40px;
  border-radius: 4px;
  font-weight: 400;
}

.system-info p {
  margin: 12px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.system-info strong {
  color: #333;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin-right: 12px;
  }

  .stat-number {
    font-size: 24px;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }
}
</style>
